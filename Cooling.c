#include "Cooling.h"
#include "Userad.h"
#include "Debug.h"
Notch_R_Typedef Notch_R;
Notch_F_Typedef Notch_F;
Notch_C_Typedef Notch_C;
Cooling_Typedef Cooling;
Cooling_Mode_Typedef Menu_Cooling_Mode;

//设置温度
int16_t RefSetTemp[Notch_R_Max] = {TEMP_Q8(2.0), TEMP_Q8(3.0), TEMP_Q8(4.0), TEMP_Q8(5.0), TEMP_Q8(6.0), TEMP_Q8(7.0), TEMP_Q8(8.0)};
int16_t FreSetTemp[Notch_F_Max + 1] = {TEMP_Q8(-16.0), TEMP_Q8(-17.0), TEMP_Q8(-18.0), TEMP_Q8(-19.0), TEMP_Q8(-20.0), TEMP_Q8(-21.0), TEMP_Q8(-22.0), TEMP_Q8(-23.0), TEMP_Q8(-24.0), TEMP_Q8(-30.0)};
int16_t ChiSetTemp[Notch_C_Max] = {TEMP_Q8(3.0), TEMP_Q8(4.0), TEMP_Q8(0.0)};

Sensor_Q8_t SNR = {0};
int16_t Ref_CutInTemp;
int16_t Ref_CutOffTemp;
int16_t Fre_CutInTemp;
int16_t Fre_CutOffTemp;
int16_t Chi_CutInTemp;
int16_t Chi_CutOffTemp;

//各环温段
AmbPara_Typedef Amb = {
	0,
	{
		{TEMPER(-50),	(TEMPER(13) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(13) - SENSOR_DELAYING_TEMP),	(TEMPER(20) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(20) - SENSOR_DELAYING_TEMP),	(TEMPER(28) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(28) - SENSOR_DELAYING_TEMP),	(TEMPER(35) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(35) - SENSOR_DELAYING_TEMP),	(TEMPER(40) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(40) - SENSOR_DELAYING_TEMP),	TEMPER(60)},
	}
};

R_FAN_AmbPara_Typedef R_FAN_Amb = {
	0,
	{
		{TEMPER(-50),	(TEMPER(8) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(8) - SENSOR_DELAYING_TEMP),	(TEMPER(13) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(13) - SENSOR_DELAYING_TEMP),	(TEMPER(28) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(28) - SENSOR_DELAYING_TEMP),	TEMPER(60)},
	}
};

Beam_AmbPara_Typedef Beam_Amb = {
	0,
	{
		{TEMPER(-50),	(TEMPER(13) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(13) - SENSOR_DELAYING_TEMP),	(TEMPER(18) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(18) - SENSOR_DELAYING_TEMP),	(TEMPER(23) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(23) - SENSOR_DELAYING_TEMP),	(TEMPER(28) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(28) - SENSOR_DELAYING_TEMP),	(TEMPER(35) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(35) - SENSOR_DELAYING_TEMP),	(TEMPER(40) + SENSOR_DELAYING_TEMP)},
		{(TEMPER(40) - SENSOR_DELAYING_TEMP),	TEMPER(60)},
	}
};

//开停机点
const int16_t Temper_R_CutIn[Notch_R_Max][Amb_Range_Max] = {
	{TEMP_Q8(4.5), TEMP_Q8(4.5), TEMP_Q8(5.0), TEMP_Q8(5.5), TEMP_Q8(6.0), TEMP_Q8(6.0)}, //2
	{TEMP_Q8(5.0), TEMP_Q8(5.0), TEMP_Q8(5.5), TEMP_Q8(6.0), TEMP_Q8(6.5), TEMP_Q8(6.5)}, //3
	{TEMP_Q8(6.0), TEMP_Q8(6.0), TEMP_Q8(6.5), TEMP_Q8(7.0), TEMP_Q8(7.5), TEMP_Q8(7.5)}, //4
	{TEMP_Q8(7.0), TEMP_Q8(7.0), TEMP_Q8(7.5), TEMP_Q8(8.0), TEMP_Q8(8.5), TEMP_Q8(8.5)}, //5
	{TEMP_Q8(8.0), TEMP_Q8(8.0), TEMP_Q8(8.5), TEMP_Q8(9.0), TEMP_Q8(9.5), TEMP_Q8(9.5)}, //6
	{TEMP_Q8(9.0), TEMP_Q8(9.0), TEMP_Q8(9.5), TEMP_Q8(10.0), TEMP_Q8(10.5), TEMP_Q8(10.5)}, //7
	{TEMP_Q8(9.5), TEMP_Q8(9.5), TEMP_Q8(10.0), TEMP_Q8(10.5), TEMP_Q8(11.0), TEMP_Q8(11.0)} //8
};
const int16_t Temper_R_CutOff[Notch_R_Max][Amb_Range_Max] = {
	{TEMP_Q8(3.5), TEMP_Q8(3.5), TEMP_Q8(4.0), TEMP_Q8(4.5), TEMP_Q8(5.0), TEMP_Q8(5.0)}, //2
	{TEMP_Q8(4.0), TEMP_Q8(4.0), TEMP_Q8(4.5), TEMP_Q8(5.0), TEMP_Q8(5.5), TEMP_Q8(5.5)}, //3
	{TEMP_Q8(5.0), TEMP_Q8(5.0), TEMP_Q8(5.5), TEMP_Q8(6.0), TEMP_Q8(6.5), TEMP_Q8(6.5)}, //4
	{TEMP_Q8(6.0), TEMP_Q8(6.0), TEMP_Q8(6.5), TEMP_Q8(7.0), TEMP_Q8(7.5), TEMP_Q8(7.5)}, //5
	{TEMP_Q8(7.0), TEMP_Q8(7.0), TEMP_Q8(7.5), TEMP_Q8(8.0), TEMP_Q8(8.5), TEMP_Q8(8.5)}, //6
	{TEMP_Q8(8.0), TEMP_Q8(8.0), TEMP_Q8(8.5), TEMP_Q8(9.0), TEMP_Q8(9.5), TEMP_Q8(9.5)}, //7
	{TEMP_Q8(8.5), TEMP_Q8(8.5), TEMP_Q8(9.0), TEMP_Q8(9.5), TEMP_Q8(10.0), TEMP_Q8(10.0)} //8
};
const int16_t Temper_Notch_0_CutIn[Amb_Range_Max] = {
	TEMP_Q8(2.5), TEMP_Q8(2.5), TEMP_Q8(3.0), TEMP_Q8(3.5), TEMP_Q8(4.0), TEMP_Q8(4.0),
};
const int16_t Temper_Notch_0_CutOff[Amb_Range_Max] = {
	TEMP_Q8(1.5), TEMP_Q8(1.5), TEMP_Q8(2.0), TEMP_Q8(2.5), TEMP_Q8(3.0), TEMP_Q8(3.0),
};
const int16_t Temper_F_CutIn[Notch_F_Max][Amb_Range_Max] = {
	{TEMP_Q8(-15.5), TEMP_Q8(-15.5), TEMP_Q8(-15.0), TEMP_Q8(-15.0), TEMP_Q8(-15.0), TEMP_Q8(-15.5)}, //-16
	{TEMP_Q8(-16.5), TEMP_Q8(-16.5), TEMP_Q8(-16.0), TEMP_Q8(-16.0), TEMP_Q8(-16.0), TEMP_Q8(-16.5)}, //-17
	{TEMP_Q8(-17.5), TEMP_Q8(-17.5), TEMP_Q8(-17.0), TEMP_Q8(-17.0), TEMP_Q8(-17.0), TEMP_Q8(-17.5)}, //-18
	{TEMP_Q8(-18.0), TEMP_Q8(-18.0), TEMP_Q8(-17.5), TEMP_Q8(-17.5), TEMP_Q8(-17.5), TEMP_Q8(-18.5)}, //-19
	{TEMP_Q8(-18.5), TEMP_Q8(-18.5), TEMP_Q8(-18.0), TEMP_Q8(-18.0), TEMP_Q8(-18.0), TEMP_Q8(-19.0)}, //-20
	{TEMP_Q8(-19.5), TEMP_Q8(-19.5), TEMP_Q8(-19.0), TEMP_Q8(-19.0), TEMP_Q8(-19.0), TEMP_Q8(-20.0)}, //-21
	{TEMP_Q8(-20.5), TEMP_Q8(-20.5), TEMP_Q8(-20.0), TEMP_Q8(-20.0), TEMP_Q8(-20.0), TEMP_Q8(-21.0)}, //-22
	{TEMP_Q8(-21.5), TEMP_Q8(-21.5), TEMP_Q8(-21.0), TEMP_Q8(-21.0), TEMP_Q8(-21.0), TEMP_Q8(-22.0)}, //-23
	{TEMP_Q8(-22.5), TEMP_Q8(-22.5), TEMP_Q8(-22.0), TEMP_Q8(-22.0), TEMP_Q8(-23.0), TEMP_Q8(-23.0)} //-24
};
const int16_t Temper_F_CutOff[Notch_F_Max][Amb_Range_Max] = {
	{TEMP_Q8(-17.5), TEMP_Q8(-17.5), TEMP_Q8(-18.0), TEMP_Q8(-18.0), TEMP_Q8(-18.0), TEMP_Q8(-18.5)}, //-16
	{TEMP_Q8(-18.5), TEMP_Q8(-18.5), TEMP_Q8(-19.0), TEMP_Q8(-19.0), TEMP_Q8(-19.0), TEMP_Q8(-19.5)}, //-17
	{TEMP_Q8(-19.5), TEMP_Q8(-19.5), TEMP_Q8(-20.0), TEMP_Q8(-20.0), TEMP_Q8(-20.0), TEMP_Q8(-20.5)}, //-18
	{TEMP_Q8(-20.0), TEMP_Q8(-20.0), TEMP_Q8(-20.5), TEMP_Q8(-20.5), TEMP_Q8(-20.5), TEMP_Q8(-21.5)}, //-19
	{TEMP_Q8(-20.5), TEMP_Q8(-20.5), TEMP_Q8(-21.0), TEMP_Q8(-21.0), TEMP_Q8(-21.0), TEMP_Q8(-22.0)}, //-20
	{TEMP_Q8(-21.5), TEMP_Q8(-21.5), TEMP_Q8(-22.0), TEMP_Q8(-22.0), TEMP_Q8(-22.0), TEMP_Q8(-23.0)}, //-21
	{TEMP_Q8(-22.5), TEMP_Q8(-22.5), TEMP_Q8(-23.0), TEMP_Q8(-23.0), TEMP_Q8(-23.0), TEMP_Q8(-24.0)}, //-22
	{TEMP_Q8(-23.5), TEMP_Q8(-23.5), TEMP_Q8(-24.0), TEMP_Q8(-24.0), TEMP_Q8(-24.0), TEMP_Q8(-25.0)}, //-23
	{TEMP_Q8(-24.5), TEMP_Q8(-24.5), TEMP_Q8(-25.0), TEMP_Q8(-25.0), TEMP_Q8(-26.0), TEMP_Q8(-26.0)} //-24
};
const int16_t Temper_SuperCold_CutIn[Amb_Range_Max] = {
	TEMP_Q8(-30.5), TEMP_Q8(-30.5), TEMP_Q8(-30.0), TEMP_Q8(-30.0), TEMP_Q8(-30.0), TEMP_Q8(-30.0)
};
const int16_t Temper_SuperCold_CutOff[Amb_Range_Max] = {
	TEMP_Q8(-32.5), TEMP_Q8(-32.5), TEMP_Q8(-33.0), TEMP_Q8(-33.0), TEMP_Q8(-33.0), TEMP_Q8(-33.0)
};
const int16_t Temper_C_CutIn[Notch_C_Max][Amb_Range_Max] = {
	{TEMP_Q8(5.5), TEMP_Q8(5.0), TEMP_Q8(4.5), TEMP_Q8(4.5), TEMP_Q8(4.5), TEMP_Q8(4.5)},
	{TEMP_Q8(6.5), TEMP_Q8(5.5), TEMP_Q8(5.0), TEMP_Q8(5.0), TEMP_Q8(5.0), TEMP_Q8(5.0)},
	{TEMP_Q8(0.5), TEMP_Q8(-1.5), TEMP_Q8(-1.5), TEMP_Q8(-2.0), TEMP_Q8(-2.0), TEMP_Q8(-3.0)},
};
const int16_t Temper_C_CutOff[Notch_C_Max][Amb_Range_Max] = {
	{TEMP_Q8(4.0), TEMP_Q8(3.5), TEMP_Q8(3.0), TEMP_Q8(3.0), TEMP_Q8(3.0), TEMP_Q8(3.0)},
	{TEMP_Q8(5.0), TEMP_Q8(4.0), TEMP_Q8(3.5), TEMP_Q8(3.5), TEMP_Q8(3.5), TEMP_Q8(3.5)},
	{TEMP_Q8(-1.0), TEMP_Q8(-3.0), TEMP_Q8(-3.0), TEMP_Q8(-3.5), TEMP_Q8(-3.5), TEMP_Q8(-4.5)},
};

//故障模式时长
const uint16_t Error_R_OnTime[Amb_Range_Max] = {TIME_10MIN, TIME_15MIN, TIME_21MIN, TIME_21MIN, TIME_22MIN, TIME_26MIN};
const uint16_t Error_R_OffTime[Amb_Range_Max] = {TIME_165MIN, TIME_130MIN, TIME_72MIN, TIME_50MIN, TIME_41MIN, TIME_44MIN};
const uint16_t Error_F_OnTime[Amb_Range_Max] = {TIME_9MIN, TIME_12MIN, TIME_20MIN, TIME_27MIN, TIME_24MIN, TIME_35MIN};
const uint16_t Error_F_OffTime[Amb_Range_Max] = {TIME_19MIN, TIME_30MIN, TIME_40MIN, TIME_46MIN, TIME_38MIN, TIME_52MIN};

//制冷初始化
void Cooling_Initial(void)
{
	Cooling.Cooling_Mode = UserPrefRefCoolingMode;
	Cooling.Notch_R = UserPrefRefNotch;
	Cooling.Notch_F = UserPrefFreNotch;
	Cooling.Notch_C = UserPrefChiNotch;
	Cooling.Notch_R_BK = Cooling.Notch_R;
	Cooling.Notch_F_BK = Cooling.Notch_F;
	Cooling.ForceCoolingFlag = 0;
	Cooling.FirstPowerOnFlag = 1;
	Cooling.Next_Amb_Range = Amb_Range_Max;
	Cooling.Pre_Amb_Range = Cooling.Next_Amb_Range;
	Cooling.R_FAN_Next_Amb_Range = R_FAN_Amb_Range_Max;
	Cooling.R_FAN_Pre_Amb_Range = Cooling.R_FAN_Next_Amb_Range;
	Cooling.Beam_Next_Amb_Range = Beam_Amb_Range_Max;
	Cooling.Beam_Pre_Amb_Range = Cooling.Beam_Next_Amb_Range;
}

//根据开停机点，获取监室制冷状态
void CoolingStatus_Calc_Service_250ms(void)
{
	Cooling.R_CutIn = Temper_R_CutIn[Cooling.Notch_R][Cooling.Amb_Range];
	Cooling.R_CutOff = Temper_R_CutOff[Cooling.Notch_R][Cooling.Amb_Range];
	Cooling.F_CutIn = Temper_F_CutIn[Cooling.Notch_F][Cooling.Amb_Range];
	Cooling.F_CutOff = Temper_F_CutOff[Cooling.Notch_F][Cooling.Amb_Range];
	Cooling.C_CutIn = Temper_C_CutIn[Cooling.Notch_C][Cooling.Amb_Range];
	Cooling.C_CutOff = Temper_C_CutOff[Cooling.Notch_C][Cooling.Amb_Range];

	if (Cooling.Cooling_Mode == SUPER_COOL) {
		Cooling.R_CutIn = Temper_R_CutIn[Notch_R_2][Cooling.Amb_Range];
		Cooling.R_CutOff = Temper_R_CutOff[Notch_R_2][Cooling.Amb_Range];
	}
	if (Cooling.Cooling_Mode == SUPER_FREEZER) {
		Cooling.F_CutIn = Temper_SuperCold_CutIn[Cooling.Amb_Range];
		Cooling.F_CutOff = Temper_SuperCold_CutOff[Cooling.Amb_Range];
	}
	if (CondesationMode.Status) {
		Cooling.R_CutIn += TEMP_Q8(0.5);
		Cooling.R_CutOff += TEMP_Q8(0.5);
		Cooling.F_CutIn += TEMP_Q8(1.0);
		Cooling.F_CutOff += TEMP_Q8(1.0);
		Cooling.C_CutIn += TEMP_Q8(0.5);
		Cooling.C_CutOff += TEMP_Q8(0.5);
	}
	if (PowerSaveMode.Status) {
		if ((PowerSaveMode.AMB_Sensor >= TEMPER(28)) && (PowerSaveMode.AMB_Sensor <= TEMPER(35))) {
			Cooling.R_CutIn += TEMP_Q8(-3.0);
			Cooling.R_CutOff += TEMP_Q8(-3.0);
		}
		if (Cooling.Notch_C == 0) {
			Cooling.C_CutIn += TEMP_Q8(-2.0);
			Cooling.C_CutOff += TEMP_Q8(-2.0);
		}
	}

	Cooling.R_CutInNew = Cooling.R_CutIn + Ref_CutInTemp;
	Cooling.R_CutOffNew = Cooling.R_CutOff + Ref_CutOffTemp;
	Cooling.F_CutInNew = Cooling.F_CutIn + Fre_CutInTemp;
	Cooling.F_CutOffNew = Cooling.F_CutOff + Fre_CutOffTemp;
	Cooling.C_CutInNew = Cooling.C_CutIn + Chi_CutInTemp;
	Cooling.C_CutOffNew = Cooling.C_CutOff + Chi_CutOffTemp;
	if ((WarnningFlag.Temp.R) && (WarnningFlag.Temp.F == 0)) {
		if (Cooling.ErrorRun.R_RunCount < Error_R_OffTime[Cooling.Amb_Range]) {
			Cooling.CoolingFlag.Bit.Ref_On = 0;
			Cooling.CoolingFlag.Bit.Ref_Off = 1;
			Cooling.RefHaveGetCutOffFlag = 1;
		} else if (Cooling.ErrorRun.R_RunCount < (Error_R_OnTime[Cooling.Amb_Range] + Error_R_OffTime[Cooling.Amb_Range])) {
			Cooling.CoolingFlag.Bit.Ref_On = 1;
			Cooling.CoolingFlag.Bit.Ref_Off = 0;
			Cooling.RefHaveGetCutOffFlag = 0;
			Cooling.R_Fan_StopFlag = 0;
			Cooling.C_CancelDefrostFlag = 0;
		} else {
			Cooling.ErrorRun.R_RunCount = 0;
		}
	} else {
		if ((PowerSaveMode.Status) && (Cooling.CompressorStatus)) {
			if (PowerSaveMode.R_DamperOnOffCount == 2) {
				if (REFRIGE_SENSOR_Q8 >= (Cooling.R_CutInNew + TEMP_Q8(3))) {
					Cooling.CoolingFlag.Bit.Ref_On = 1;
					Cooling.CoolingFlag.Bit.Ref_Off = 0;
					Cooling.RefHaveGetCutOffFlag = 0;
					Cooling.R_Fan_StopFlag = 0;
					Cooling.C_CancelDefrostFlag = 0;
				} else if (REFRIGE_SENSOR_Q8 <= Cooling.R_CutOffNew) {
					Cooling.CoolingFlag.Bit.Ref_On = 0;
					Cooling.CoolingFlag.Bit.Ref_Off = 1;
					Cooling.RefHaveGetCutOffFlag = 1;
				} else {
				}
			} else {
				if (REFRIGE_SENSOR_Q8 >= Cooling.R_CutInNew) {
					Cooling.CoolingFlag.Bit.Ref_On = 1;
					Cooling.CoolingFlag.Bit.Ref_Off = 0;
					Cooling.RefHaveGetCutOffFlag = 0;
					Cooling.R_Fan_StopFlag = 0;
					Cooling.C_CancelDefrostFlag = 0;
					PowerSaveMode.R_DamperOnOffCount = 1;
				} else if (REFRIGE_SENSOR_Q8 <= Cooling.R_CutOffNew) {
					Cooling.CoolingFlag.Bit.Ref_On = 0;
					Cooling.CoolingFlag.Bit.Ref_Off = 1;
					Cooling.RefHaveGetCutOffFlag = 1;
					if (PowerSaveMode.R_DamperOnOffCount == 1) {
						PowerSaveMode.R_DamperOnOffCount = 2;
					}
				} else {
				}
			}
		} else {
			if (REFRIGE_SENSOR_Q8 >= Cooling.R_CutInNew) {
				Cooling.CoolingFlag.Bit.Ref_On = 1;
				Cooling.CoolingFlag.Bit.Ref_Off = 0;
				Cooling.RefHaveGetCutOffFlag = 0;
				Cooling.R_Fan_StopFlag = 0;
				Cooling.C_CancelDefrostFlag = 0;
			} else if (REFRIGE_SENSOR_Q8 <= Cooling.R_CutOffNew) {
				Cooling.CoolingFlag.Bit.Ref_On = 0;
				Cooling.CoolingFlag.Bit.Ref_Off = 1;
				Cooling.RefHaveGetCutOffFlag = 1;
			} else {
			}
		}
	}

	if ((WarnningFlag.Temp.F) && (WarnningFlag.Temp.R == 0)) {
		if (Cooling.ErrorRun.F_RunCount < Error_F_OffTime[Cooling.Amb_Range]) {
			if (Cooling.CoolingFlag.Bit.Ref_On) {
				Cooling.CoolingFlag.Bit.Freezer_On = 1;
				Cooling.CoolingFlag.Bit.Freezer_Off = 0;
			} else {
				Cooling.CoolingFlag.Bit.Freezer_On = 0;
				Cooling.CoolingFlag.Bit.Freezer_Off = 1;
			}
		} else if (Cooling.ErrorRun.F_RunCount < (Error_F_OnTime[Cooling.Amb_Range] + Error_F_OffTime[Cooling.Amb_Range])) {
			Cooling.CoolingFlag.Bit.Freezer_On = 1;
			Cooling.CoolingFlag.Bit.Freezer_Off = 0;
		} else {
			Cooling.ErrorRun.F_RunCount = 0;
		}
	} else {
		if (FREEZE_SENSOR_Q8 >= Cooling.F_CutInNew) {
			Cooling.CoolingFlag.Bit.Freezer_On = 1;
			Cooling.CoolingFlag.Bit.Freezer_Off = 0;
		} else if (FREEZE_SENSOR_Q8 <= Cooling.F_CutOffNew) {
			Cooling.CoolingFlag.Bit.Freezer_On = 0;
			Cooling.CoolingFlag.Bit.Freezer_Off = 1;
		} else {
		}
	}

	if (CHILD_SENSOR_Q8 >= Cooling.C_CutInNew) {
		Cooling.CoolingFlag.Bit.C_On = 1;
		Cooling.CoolingFlag.Bit.C_Off = 0;
		Cooling.ChildHasGetCutOffFlag = 0;
	} else if (CHILD_SENSOR_Q8 <= Cooling.C_CutOffNew) {
		Cooling.CoolingFlag.Bit.C_On = 0;
		Cooling.CoolingFlag.Bit.C_Off = 1;
		Cooling.ChildHasGetCutOffFlag = 1;
	} else {
	}

//获取减霜模式退出温度
	if (FDFunctionCount == 1) {
		Cooling.R_Fan_StopTemp = TEMP_Q8(10);
	} else {
		if (Cooling.ValveForceChangeFlag) {
			if (Cooling.HighExitTempFlag) {
				Cooling.R_Fan_StopTemp = TEMP_Q8(8);
			} else {
				if (DoorSwitch_Open_Count >= 5) {
					Cooling.R_Fan_StopTemp = TEMP_Q8(6);
				} else {
					if (Cooling.R_FAN_Amb_Range == 0) {
						Cooling.R_Fan_StopTemp = TEMP_Q8(3);
					} else {
						if (Cooling.Notch_R <= Notch_R_5) {
							Cooling.R_Fan_StopTemp = TEMP_Q8(3);
						} else {
							Cooling.R_Fan_StopTemp = (RefSetTemp[Cooling.Notch_R] - TEMP_Q8(2.0));
						}
					}
				}
			}
		} else {
			if (Cooling.R_FAN_Amb_Range == 0) {
				Cooling.R_Fan_StopTemp = TEMP_Q8(3);
			} else {
				if (Cooling.Notch_R <= Notch_R_5) {
					Cooling.R_Fan_StopTemp = TEMP_Q8(3);
				} else {
					Cooling.R_Fan_StopTemp = (RefSetTemp[Cooling.Notch_R] - TEMP_Q8(2.0));
				}
			}
		}
	}

	if (Cooling.R_FAN_Amb_Range == 0) {
		if (Cooling.AfterDefrostCompRunFlag == 0) {
			if (Cooling.LowAmbCompConRunCount >= TIME_1HR) {
				Cooling.CompRunCoolingFlag = 1;
			}
		} else {
			if (Cooling.LowAmbCompConRunCount >= TIME_2HR) {
				Cooling.CompRunCoolingFlag = 1;
			}
		}
	}
	//根据环温获取电动阀在冷藏侧最长时间
	if ((Cooling.Amb_Range == Amb_Range_13) || (Cooling.Amb_Range == Amb_Range_20)) {
		Cooling.MaxValveStayInRefTime = TIME_20MIN;
	} else if (Cooling.Amb_Range == Amb_Range_28) {
		Cooling.MaxValveStayInRefTime = TIME_30MIN;
	} else if ((Cooling.Amb_Range == Amb_Range_35) || (Cooling.Amb_Range == Amb_Range_40)) {
		Cooling.MaxValveStayInRefTime = TIME_40MIN;
	} else {
		Cooling.MaxValveStayInRefTime = TIME_60MIN;
	}
}
//压缩机状态刷新，冷藏风门和变温风门状态刷新
void Device_Calc_Service_250ms(void)
{
	if (ProductionMode.Status) {
		Defrost.CompRunRefNoOffFlag = 0;
		return;
	}
	if (Defrost.Status.TotalFlag) return;
	if (Cooling.ForceCoolingFlag) return;
	if (WarnningFlag.Temp.R && WarnningFlag.Temp.F) return;
	if ((Cooling.CoolingFlag.Bit.Freezer_On == 1) && (Cooling.CompressorDelayFlag == 0)) {
		Cooling.FreezerMakeCompOnFlag = 1;
		if (Cooling.CompressorStatus == 0) {
			Cooling.CompressorDelayOnFlag = 1;
		}
		if (Cooling.CompressorDelayOnCount >= 10) {
			if (Cooling.CompressorStatus == 0) {
				if (REFRIGE_SENSOR_Q8 > Cooling.R_CutOffNew) {
					Cooling.CoolingFlag.Bit.Ref_On = 1;
					Cooling.CoolingFlag.Bit.Ref_Off = 0;
					Cooling.RefHaveGetCutOffFlag = 0;
					Cooling.R_Fan_StopFlag = 0;
				}
			}
			if (Cooling.CompressorStatus) {
				Cooling.F_MakeCompOnTimeFlag = 0;
				Cooling.F_MakeCompOnProcessFlag = 1;
			}
			if ((Cooling.CompStartFlag == 0) && (Cooling.CompressorStatus == 0)) {
				Cooling.CompStartFlag = 1;
				Cooling.F_MakeCompOnTimeFlag = 1;
			}
			Cooling.CompressorStatus = 1;
			Cooling.CompressorHasOnFlag = 1;
		}
	}

	if ((PowerSaveMode.Status == 0) && (REFRIGE_SENSOR_Q8 >= (Cooling.R_CutInNew + TEMP_Q8(0.5)))  && (Cooling.CompressorDelayFlag == 0) && (FREEZE_SENSOR_Q8 > Cooling.F_CutOffNew)) {
		Cooling.FridgeMakeCompOnFlag = 1;
		if (FREEZE_SENSOR_Q8 > Cooling.F_CutOffNew) {
			Cooling.CoolingFlag.Bit.Freezer_Off = 0;
		}
		if (Cooling.CompressorStatus == 0) {
			Cooling.CompressorDelayOnFlag = 1;
		}
		if (Cooling.CompressorDelayOnCount >= 10) {
			if (Cooling.CompressorStatus) {
				Cooling.R_MakeCompOnTimeFlag = 0;
				Cooling.R_MakeCompOnProcessFlag = 1;
			}
			if ((Cooling.CompStartFlag == 0) && (Cooling.CompressorStatus == 0)) {
				Cooling.CompStartFlag = 1;
				Cooling.R_MakeCompOnTimeFlag = 1;
			}
			Cooling.CompressorStatus = 1;
			Cooling.CompressorHasOnFlag = 1;
		}
	}
	//冷冻达到停机点时，压缩机停机
	if (Cooling.FridgeMakeCompOnFlag) {
		if (Cooling.CoolingFlag.Bit.Freezer_Off == 1) {
			if (Cooling.CompressorStatus == 1) {
				Cooling.CompressorStatus = 0;
				Cooling.CompressorDelayOnFlag = 0;
				Inverter.HighTempFlag = 0;
				Inverter.HighLoadFlag = 0;
				Cooling.CompressorDelayFlag = 1;
				Defrost.AfterDefrostFisrtOnFlag = 0;
				Defrost.AmbNotSearchFlag = 0;
				if (Cooling.CompressorHasOnFlag) {
					Inverter.FirstPowerOn = 0;
					Cooling.FirstPowerOnFlag = 0;
				}
				Cooling.FreezerMakeCompOnFlag = 0;
				Cooling.FridgeMakeCompOnFlag = 0;
				Cooling.R_Fan_StartDelayFlag = 0;
				Cooling.F_Fan_StartDelayFlag = 0;
				Cooling.F_Fan_ValveDelayFlag = 0;
				Cooling.AfterDefrostCompRunFlag = 0;
				Cooling.LowAmbCompConRunCount = 0;
				Cooling.CompRun4hCount = 0;
				PowerSaveMode.R_DamperOnOffCount = 0;
				Cooling.CompStartFlag = 0;
				Cooling.C_MakeValveChangeFlag = 0;
				Cooling.R_MakeCompOnProcessFlag = 0;
				Cooling.F_MakeCompOnProcessFlag = 0;
				Cooling.CompSpeedProductionFlag = 0;
			}
		}
	} else {
		if (Cooling.CoolingFlag.Bit.Freezer_Off == 1) {
			if (Cooling.CompressorStatus == 1) {
				Cooling.CompressorStatus = 0;
				Cooling.CompressorDelayOnFlag = 0;
				Inverter.HighTempFlag = 0;
				Inverter.HighLoadFlag = 0;
				Cooling.CompressorDelayFlag = 1;
				Defrost.AfterDefrostFisrtOnFlag = 0;
				Defrost.AmbNotSearchFlag = 0;
				if (Cooling.CompressorHasOnFlag) {
					Inverter.FirstPowerOn = 0;
					Cooling.FirstPowerOnFlag = 0;
				}
				Cooling.FreezerMakeCompOnFlag = 0;
				Cooling.FridgeMakeCompOnFlag = 0;
				Cooling.R_Fan_StartDelayFlag = 0;
				Cooling.F_Fan_StartDelayFlag = 0;
				Cooling.F_Fan_ValveDelayFlag = 0;
				Cooling.AfterDefrostCompRunFlag = 0;
				Cooling.LowAmbCompConRunCount = 0;
				Cooling.CompRun4hCount = 0;
				PowerSaveMode.R_DamperOnOffCount = 0;
				Cooling.CompStartFlag = 0;
				Cooling.C_MakeValveChangeFlag = 0;
				Cooling.R_MakeCompOnProcessFlag = 0;
				Cooling.F_MakeCompOnProcessFlag = 0;
				Cooling.CompSpeedProductionFlag = 0;
			}
		}
	}

	if (((DoorSwitch_FridgeLeft.Status == 1) && (DoorSwitch_FridgeLeft.OpenTimer_Continuous <= TIME_2MIN)) \
			|| ((DoorSwitch_FridgeRight.Status == 1) && (DoorSwitch_FridgeRight.OpenTimer_Continuous <= TIME_2MIN))) {
	} else {
		//冷藏风门状态更新，先开后关
		if (Cooling.Valve_Status == Valve_CO_Mode) {
			Cooling.R_Damper_Status = 1;
		}
		if (Cooling.CompRunCoolingFlag) {
			if (Cooling.CompRunCoolingCount <= TIME_20MIN) {
				Cooling.R_Damper_Status = 1;
			}
		}
		if (Cooling.FirstPowerOnFlag == 1) {
			if (Cooling.PowerOn_Count >= TIME_30MIN) {
				Cooling.R_Damper_Status = 1;
			}
		}
		if (PowerSaveMode.Status) {
			if ((Cooling.Valve_Status == Valve_OC_Mode) && ((Cooling.ReduceFrostModeCount < TIME_10MIN))  && (Cooling.Valve_ChangeStatusFlag || Cooling.ValveForceChangeFlag)) {
				Cooling.R_Damper_Status = 1;
			}
		}
		//减霜模式时，冷藏风门开启
		if ((Cooling.Valve_ChangeStatusFlag) || (Cooling.ValveForceChangeFlag)) {
			if (Cooling.R_Fan_StopFlag == 0) {
				Cooling.R_Damper_Status = 1;
			}
		}
		if (WarnningFlag.Temp.RD) {
		} else {
			//减霜模式结束，冷藏风门关闭
			if (Cooling.R_Fan_StopFlag) {
				Cooling.R_Damper_Status = 0;
			}
		}

		if (Cooling.FirstPowerOnFlag == 1) {
			if (Cooling.PowerOn_Count < TIME_30MIN) {
				Cooling.R_Damper_Status = 0;
			}
		}

		if (WarnningFlag.Temp.RD) {
		} else {
			//变温取消减霜模式特殊控制
			if (PowerSaveMode.Status) {
				if ((Cooling.Valve_Status == Valve_OC_Mode) && ((Cooling.C_CancelDefrostFlag) || (Cooling.ReduceFrostModeCount >= TIME_10MIN))  && (Cooling.Valve_ChangeStatusFlag || Cooling.ValveForceChangeFlag)) {
					Cooling.R_Damper_Status = 0;
				}
			} else {
				if ((Cooling.Valve_Status == Valve_OC_Mode) && (Cooling.C_CancelDefrostFlag) && (Cooling.Valve_ChangeStatusFlag || Cooling.ValveForceChangeFlag)) {
					Cooling.R_Damper_Status = 0;
				}
			}
		}
		if ((Cooling.Valve_ChangeStatusFlag == 0) && (Cooling.ValveForceChangeFlag == 0) && (Cooling.CompressorStatus == 0)) {
			Cooling.R_Damper_Status = 0;
		}
		if ((Cooling.RefHaveGetCutOffFlag) && (Cooling.ChildHasGetCutOffFlag == 0) && (Cooling.Valve_Status == Valve_CO_Mode)) {
			Cooling.R_Damper_Status = 0;
		}

		//变温风门状态刷新
		if (Cooling.FirstPowerOnFlag == 1) {
			if (Cooling.PowerOn_Count >= TIME_30MIN) {
				Cooling.C_Damper_Status = 1;
			}
		}
		if (Cooling.CoolingFlag.Bit.C_On == 1) {
			if (Cooling.Valve_Status == Valve_CO_Mode) {
				Cooling.C_Damper_Status = 1;
			}
		} else if (Cooling.CoolingFlag.Bit.C_Off == 1) {
			Cooling.C_Damper_Status = 0;
		}
		if (Cooling.FirstPowerOnFlag == 1) {
			if (Cooling.PowerOn_Count < TIME_30MIN) {
				Cooling.C_Damper_Status = 0;
			}
		}
		if ((WarnningFlag.Temp.VV) || (Cooling.Valve_Status != Valve_CO_Mode)) {
			Cooling.C_Damper_Status = 0;
		}
		if (((DoorSwitch_FridgeLeft.Status == 1) && (DoorSwitch_FridgeLeft.OpenTimer_Continuous >= TIME_2MIN) && (DoorSwitch_FridgeLeft.OpenTimer_Continuous < TIME_10MIN)) \
				|| ((DoorSwitch_FridgeRight.Status == 1) && (DoorSwitch_FridgeRight.OpenTimer_Continuous >= TIME_2MIN) && (DoorSwitch_FridgeRight.OpenTimer_Continuous < TIME_10MIN))) {
			Cooling.R_Damper_Status = 0;
			Cooling.C_Damper_Status = 0;
		}
		if (CHILD_SENSOR_Q8 >= Cooling.C_CutInNew) {
			Cooling.ChildHasGetCutOffFlag = 0;
			if (Cooling.R_MakeCompOnProcessFlag) {
				Cooling.C_MakeValveChangeFlag = 0;
			} else {
				if (Cooling.F_MakeCompOnTimeFlag) {
					if (REFRIGE_SENSOR_Q8 <= Cooling.R_CutOffNew) {
						if (Cooling.R_FAN_Amb_Range == 1) {
							Cooling.C_MakeValveChangeFlag = 1;
						}
					} else {
						Cooling.C_MakeValveChangeFlag = 0;
					}
				} else if (Cooling.F_MakeCompOnProcessFlag) {
					Cooling.C_MakeValveChangeFlag = 0;
				}
			}
		}
	}
	//展厅功能关闭负载
	if (DemoFunctionOnFlag == 1) {
		Cooling.CompressorStatus = 0;
		Cooling.R_Damper_Status = 0;
		Cooling.C_Damper_Status = 0;
		Defrost.LoadStatus = 0;
		Defrost.R_LoadStatus = 0;
		Cooling.CompSpeedProductionFlag = 0;
	}
}

//电动阀状态刷新
void Cooling_Valve_Service_250ms(void)
{
	//查询模式时显示电动阀状态
	if (Cooling.Valve_Status == Valve_OO_Mode) {
		Menu_DisplayValveStatus = 3;
	} else if (Cooling.Valve_Status == Valve_OC_Mode) {
		Menu_DisplayValveStatus = 2;
	} else if (Cooling.Valve_Status == Valve_CO_Mode) {
		Menu_DisplayValveStatus = 1;
	} else if (Cooling.Valve_Status == Valve_CC_Mode) {
		Menu_DisplayValveStatus = 0;
	}
	if (ProductionMode.Status) return;
	if (Defrost.Status.TotalFlag) return;
	if (Cooling.ForceCoolingFlag) return;
	if (WarnningFlag.Temp.R && WarnningFlag.Temp.F) return;
	if ((Cooling.CompressorStatus) && (((Cooling.Valve_ChangeStatusFlag == 0) && (Cooling.CoolingFlag.Bit.Ref_On)) || (Cooling.PowerSaveCompStopFlag))) {
		if (PowerSaveMode.Status) {
			if (PowerSaveMode.R_DamperOnOffCount == 2) {
				if (Cooling.CoolingFlag.Bit.Ref_On == 1) {
					Cooling.Valve_Status = Valve_CO_Mode;
				} else {
					if (Cooling.Valve_Status == Valve_CO_Mode) {
						if (Cooling.Valve_ChangeStatusFlag == 0) {
							Cooling.Valve_ChangeStatusFlag = 1;
#ifdef USE_WIFI
							wifiUploadRDTempTime = 0;
#endif
						}
					}
					if (Cooling.ChildHasGetCutOffFlag) {
						Cooling.Valve_Status = Valve_OC_Mode;
					} else {
						Cooling.Valve_Status = Valve_OC_Mode;
					}
				}
			} else {
				Cooling.Valve_Status = Valve_CO_Mode;
			}
			Cooling.PowerSaveCompStopFlag = 0;
		} else {
			Cooling.Valve_Status = Valve_CO_Mode;
		}
	}
	//减霜模式结束后，阀才能切换回冷藏侧
	if ((Cooling.CompressorStatus) && (Cooling.CoolingFlag.Bit.Ref_On == 1) && (Cooling.ValveForceChangeFlag == 0) && (Cooling.Valve_ChangeStatusFlag == 0)) {
		Cooling.Valve_Status = Valve_CO_Mode;
		Cooling.PowerSaveCompStopFlag = 0;
	}

	//特殊逻辑，变温取消减霜模式
	if (Cooling.Valve_Status == Valve_OC_Mode) {
		if (Cooling.C_MakeValveChangeFlag) {
			Cooling.Valve_Status = Valve_CO_Mode;
			Cooling.C_MakeValveChangeFlag = 0;
			Cooling.C_CancelDefrostFlag = 1;
		}
	}

	//初次上电逻辑
	if (Cooling.FirstPowerOnFlag == 1) {
		if (Cooling.PowerOn_Count < TIME_30MIN) {
			Cooling.Valve_Status = Valve_OC_Mode;
		} else {
			Cooling.Valve_Status = Valve_CO_Mode;
		}
	}

	//冷藏和变温达到过停机点后，电动阀切换到冷冻侧
	if ((Cooling.RefHaveGetCutOffFlag) && (Cooling.ChildHasGetCutOffFlag)) {
		if (Cooling.Valve_Status == Valve_CO_Mode) {
			if (Cooling.Valve_ChangeStatusFlag == 0) {
				Cooling.Valve_ChangeStatusFlag = 1;
#ifdef USE_WIFI
				wifiUploadRDTempTime = 0;
#endif
			}
		}
		Cooling.Valve_Status = Valve_OC_Mode;
	}

	if (Cooling.R_FAN_Amb_Range == 0) {
		if (Cooling.CompRunCoolingFlag) {
			if (Cooling.CompRunCoolingCount <= TIME_20MIN) {
				Cooling.Valve_Status = Valve_CO_Mode;
			} else {
				if (Cooling.Valve_Status == Valve_CO_Mode) {
					if (Cooling.Valve_ChangeStatusFlag == 0) {
						Cooling.Valve_ChangeStatusFlag = 1;
#ifdef USE_WIFI
						wifiUploadRDTempTime = 0;
#endif
					}
				}
				Cooling.Valve_Status = Valve_OC_Mode;
				Cooling.LowAmbCompConRunCount = 0;
				Cooling.CompRunCoolingFlag = 0;
				Cooling.CompRunCoolingCount = 0;
			}
		}
	} else {
		Cooling.LowAmbCompConRunCount = 0;
		Cooling.CompRunCoolingCount = 0;
		Cooling.CompRunCoolingFlag = 0;
	}
	//电动阀在冷藏侧达到最大时间后，强制切换到冷冻侧开始减霜模式
	if (Cooling.ValveStayInRefCount >= Cooling.MaxValveStayInRefTime) {
		if (Cooling.Valve_Status == Valve_CO_Mode) {
			if (Cooling.Valve_ChangeStatusFlag == 0) {
				Cooling.Valve_ChangeStatusFlag = 1;
				Cooling.ValveForceChangeFlag = 1;
				if ((REFDEF_SENSOR_Q8 < TEMP_Q8(-20)) && (REFRIGE_SENSOR_Q8 > TEMP_Q8(15))) {
					Cooling.HighExitTempFlag = 1;
				}
#ifdef USE_WIFI
				wifiUploadRDTempTime = 0;
#endif
			}
		}
		Cooling.Valve_Status = Valve_OC_Mode;
		Cooling.ValveStayInRefCount = 0;
	}

	//冷藏蒸发器低温保护，强制切换到冷冻侧开始减霜模式
	if (Cooling.RefDefLowTempCount >= TIME_5MIN) {
		Cooling.Valve_Status = Valve_OC_Mode;
		Cooling.Valve_ChangeStatusFlag = 1;
		Cooling.RefDefLowTempCount = 0;
		Cooling.R_Fan_StopFlag = 0;
		Cooling.FirstPowerOnFlag = 0;
#ifdef USE_WIFI
		wifiUploadRDTempTime = 0;
#endif
	}

	//压缩机停机时，非能耗下切换到冷冻侧开始减霜模式
	if (Cooling.CompressorStatus == 0) {
		if (PowerSaveMode.Status == 0) {
			if (Cooling.Valve_Status == Valve_CO_Mode) {
				if (Cooling.Valve_ChangeStatusFlag == 0) {
					Cooling.Valve_ChangeStatusFlag = 1;
#ifdef USE_WIFI
					wifiUploadRDTempTime = 0;
#endif
				}
			}
			Cooling.Valve_Status = Valve_OC_Mode;
		} else {
			Cooling.Valve_Status = Valve_CC_Mode;
			Cooling.PowerSaveCompStopFlag = 1;
		}
	}

	//强制化霜模式H1
	if (FDFunctionCount == 1) {
		Cooling.Valve_Status = Valve_OC_Mode;
		if (Cooling.Valve_ChangeStatusFlag == 0) {
			Cooling.Valve_ChangeStatusFlag = 1;
#ifdef USE_WIFI
			wifiUploadRDTempTime = 0;
#endif
		}
		Cooling.R_Fan_StopFlag = 0;
	}
}

//风机状态刷新（先开后关）
void Cooling_Fan_Service_250ms(void)
{
	if (ProductionMode.Status) return;
	if (Defrost.Status.TotalFlag) return;
	if (Cooling.ForceCoolingFlag) return;
	if (WarnningFlag.Temp.R && WarnningFlag.Temp.F) return;
	if ((Cooling.Valve_Status == Valve_CO_Mode) || ((Cooling.Valve_ChangeStatusFlag  || Cooling.ValveForceChangeFlag) && (Cooling.Valve_Status == Valve_OC_Mode)) \
			|| ((Cooling.CompRunCoolingFlag) && (Cooling.CompRunCoolingCount <= TIME_20MIN))) {
		if (((DoorSwitch_FridgeLeft.Status == 0) && (DoorSwitch_FridgeLeft.CloseTimer_Continuous >= 15) && (DoorSwitch_FridgeRight.CloseTimer_Continuous >= 15)) || \
				((DoorSwitch_FridgeLeft.Status) && (DoorSwitch_FridgeLeft.OpenTimer_Continuous >= TIME_7MIN))) {
			if ((Cooling.R_Fan_Status == 0) && (Cooling.R_FanStartFlag == 0)) {
				Cooling.R_FanStartFlag = 1;
			}
			Cooling.R_Fan_Status = 1;
		}
		if (((DoorSwitch_FridgeRight.Status == 0) && (DoorSwitch_FridgeRight.CloseTimer_Continuous >= 15) && (DoorSwitch_FridgeLeft.CloseTimer_Continuous >= 15)) || \
				((DoorSwitch_FridgeRight.Status) && (DoorSwitch_FridgeRight.OpenTimer_Continuous >= TIME_7MIN))) {
			if ((Cooling.R_Fan_Status == 0) && (Cooling.R_FanStartFlag == 0)) {
				Cooling.R_FanStartFlag = 1;
			}
			Cooling.R_Fan_Status = 1;
		}
	}
	if ((Cooling.CompStartFlag == 1) && (Cooling.R_FanStartFlag == 1)) {
		Cooling.R_Fan_StartDelayFlag = 1;
	}
	if (PowerSaveMode.Status) {
		if ((Cooling.Valve_Status == Valve_OC_Mode) && ((Cooling.ReduceFrostModeCount < TIME_10MIN))  && (Cooling.Valve_ChangeStatusFlag || Cooling.ValveForceChangeFlag)) {
			Cooling.R_Fan_Status = 1;
		}
	}
	if (Cooling.FirstPowerOnFlag == 1) {
		if (Cooling.PowerOn_Count >= TIME_30MIN) {
			if ((Cooling.R_Fan_Status == 0) && (Cooling.R_FanStartFlag == 0)) {
				Cooling.R_FanStartFlag = 1;
			}
			Cooling.R_Fan_Status = 1;
		} else {
			Cooling.R_Fan_Status = 0;
		}
	}
	if (Cooling.R_Fan_StartDelayFlag) {
		if (Cooling.R_Fan_StartDelayCount <= TIME_60S) {
			Cooling.R_Fan_Status = 0;
		}
	}

	if (WarnningFlag.Temp.RD) {
		//冷藏化霜传感器故障时，冷藏请求制冷，减霜模式退出，冷藏风机关闭
		if ((Cooling.Valve_ChangeStatusFlag) || (Cooling.ValveForceChangeFlag)) {
			if (Cooling.CoolingFlag.Bit.Ref_On) {
				Cooling.R_Fan_Status = 0;
				Cooling.R_Fan_StartDelayFlag = 0;
				Cooling.R_Fan_StopFlag = 1;
				Cooling.ValveForceChangeFlag = 0;
				Cooling.Valve_ChangeStatusFlag = 0;
				Cooling.R_FanStartFlag = 0;
				Cooling.HighExitTempFlag = 0;
#ifdef USE_WIFI
				wifiUploadRDTempTime = 0;
#endif
			}
		}
	} else {
		//冷藏化霜传感器达到退出温度，减霜模式退出，冷藏风机关闭
		if ((Cooling.Valve_ChangeStatusFlag == 1) && (REFDEF_SENSOR_Q8 >= Cooling.R_Fan_StopTemp)) {
			Cooling.R_Fan_Status = 0;
			Cooling.R_Fan_StartDelayFlag = 0;
			Cooling.R_Fan_StopFlag = 1;
			Cooling.ValveForceChangeFlag = 0;
			Cooling.Valve_ChangeStatusFlag = 0;
			Cooling.R_FanStartFlag = 0;
			Cooling.HighExitTempFlag = 0;
			if (FDFunctionCount == 1) {
				FDFunctionCount = 0;
				Menu_FDFunction = FDFunctionNone;
				Defrost.ForceDefrostFlag = 0;
				Menu_FDFunctionStart = 0;
			}
#ifdef USE_WIFI
			wifiUploadRDTempTime = 0;
#endif
		}

		//特殊模式，变温控制阀切换
		if (PowerSaveMode.Status) {
			if ((Cooling.Valve_Status == Valve_OC_Mode) && ((Cooling.C_CancelDefrostFlag) || (Cooling.ReduceFrostModeCount >= TIME_10MIN)) && (Cooling.Valve_ChangeStatusFlag || Cooling.ValveForceChangeFlag)) {
				Cooling.R_Fan_Status = 0;
				Cooling.R_Fan_StartDelayFlag = 0;
				Cooling.R_Fan_StopFlag = 1;
				Cooling.ValveForceChangeFlag = 0;
				Cooling.Valve_ChangeStatusFlag = 0;
				Cooling.R_FanStartFlag = 0;
#ifdef USE_WIFI
				wifiUploadRDTempTime = 0;
#endif
			}
		} else {
			if ((Cooling.Valve_Status == Valve_OC_Mode) && (Cooling.C_CancelDefrostFlag) && (Cooling.Valve_ChangeStatusFlag || Cooling.ValveForceChangeFlag)) {
				Cooling.R_Fan_Status = 0;
				Cooling.R_Fan_StartDelayFlag = 0;
				Cooling.R_Fan_StopFlag = 1;
				Cooling.ValveForceChangeFlag = 0;
				Cooling.Valve_ChangeStatusFlag = 0;
				Cooling.R_FanStartFlag = 0;
#ifdef USE_WIFI
				wifiUploadRDTempTime = 0;
#endif
			}
		}
	}

	if ((Cooling.Valve_ChangeStatusFlag == 0) && (Cooling.ValveForceChangeFlag == 0) && (Cooling.CompressorStatus == 0)) {
		Cooling.R_Fan_Status = 0;
		Cooling.R_FanStartFlag = 0;
	}
	if ((DoorSwitch_FridgeLeft.Status == 1) && (DoorSwitch_FridgeLeft.OpenTimer_Continuous < TIME_7MIN)) {
		Cooling.R_Fan_Status = 0;
		Cooling.R_FanStartFlag = 0;
	}
	if ((DoorSwitch_FridgeRight.Status == 1) && (DoorSwitch_FridgeRight.OpenTimer_Continuous < TIME_7MIN)) {
		Cooling.R_Fan_Status = 0;
		Cooling.R_FanStartFlag = 0;
	}

	//冷冻风机控制逻辑
	if (((Cooling.CompressorStatus) && (Cooling.CoolingFlag.Bit.Freezer_On == 1)) || (Cooling.Valve_ChangeStatusFlag || Cooling.FirstPowerOnFlag)) {
		if (((DoorSwitch_FreezerLeft.Status == 0) && (DoorSwitch_FreezerLeft.CloseTimer_Continuous >= 15) && (DoorSwitch_FreezerRight.CloseTimer_Continuous >= 15)) || \
				((DoorSwitch_FreezerLeft.Status) && (DoorSwitch_FreezerLeft.OpenTimer_Continuous >= TIME_7MIN))) {
			if ((Cooling.F_FanStatus == 0) && (Cooling.F_FanStartFlag == 0)) {
				Cooling.F_FanStartFlag = 1;
			}
			Cooling.F_FanStatus = 1;
		}
		if (((DoorSwitch_FreezerRight.Status == 0) && (DoorSwitch_FreezerRight.CloseTimer_Continuous >= 15) && (DoorSwitch_FreezerLeft.CloseTimer_Continuous >= 15)) || \
				((DoorSwitch_FreezerRight.Status) && (DoorSwitch_FreezerRight.OpenTimer_Continuous >= TIME_7MIN))) {
			if ((Cooling.F_FanStatus == 0) && (Cooling.F_FanStartFlag == 0)) {
				Cooling.F_FanStartFlag = 1;
			}
			Cooling.F_FanStatus = 1;
		}
	}

	if ((Cooling.CompStartFlag == 1) && (Cooling.F_FanStartFlag == 1) && (Cooling.Valve_Status == Valve_CO_Mode)) {
		Cooling.F_Fan_ValveDelayFlag = 1;
	}
	if ((Cooling.Valve_Status == Valve_CO_Mode) && (Cooling.CompressorStatus)) {
		if (FREEZE_SENSOR_Q8 >= TEMP_Q8(-15)) {
			Cooling.F_FanStatus = 1;
			if ((Cooling.F_FanStatus == 0) && (Cooling.F_FanStartFlag == 0)) {
				Cooling.F_FanStartFlag = 1;
			}
			Cooling.F_Fan_StartFlag = 1;
		} else {
			if (Cooling.F_Fan_StartFlag == 0) {
				Cooling.F_FanStatus = 0;
				Cooling.F_FanStartFlag = 0;
				Cooling.F_Fan_ValveDelayFlag = 0;
			}
		}
	}

	if (Cooling.F_Fan_ValveDelayFlag) {
		if (Cooling.F_Fan_ValveDelayCount <= TIME_60S) {
			Cooling.F_FanStatus = 0;
		}
	}

	if (Cooling.CoolingFlag.Bit.Freezer_Off == 1) {
		Cooling.F_FanStatus = 0;
		Cooling.F_Fan_StartFlag = 0;
		Cooling.F_FanStartFlag = 0;
	}
	if ((DoorSwitch_FreezerLeft.Status == 1) && (DoorSwitch_FreezerLeft.OpenTimer_Continuous < TIME_7MIN)) {
		Cooling.F_FanStatus = 0;
		Cooling.F_FanStartFlag = 0;
	}
	if ((DoorSwitch_FreezerRight.Status == 1) && (DoorSwitch_FreezerRight.OpenTimer_Continuous < TIME_7MIN)) {
		Cooling.F_FanStatus = 0;
		Cooling.F_FanStartFlag = 0;
	}

	//冷凝风机控制逻辑
	if (Cooling.Amb_Range == 0) {
		Cooling.C_Fan_Status = 0;
	} else {
		if (Cooling.CompressorStatus) {
			if (CondesationMode.Status) {
				if (Cooling.CompressorRunTimeCount >= TIME_8MIN) {
					if (Cooling.CondesationModeFanDelayCount <= TIME_5MIN) {
						Cooling.C_Fan_Status = 1;
					} else if (Cooling.CondesationModeFanDelayCount <= TIME_8MIN) {
						Cooling.C_Fan_Status = 0;
					} else {
						Cooling.CondesationModeFanDelayCount = 0;
					}
				} else {
					Cooling.C_Fan_Status = 0;
				}
			} else {
				if ((Cooling.Amb_Range == 1) && (PowerSaveMode.Status == 0)) {
					if (Cooling.CompressorRunTimeCount >= TIME_5MIN) {
						Cooling.C_Fan_Status = 1;
					} else {
						Cooling.C_Fan_Status = 0;
					}
				} else {
					if (Cooling.CompressorRunTimeCount >= TIME_1MIN) {
						Cooling.C_Fan_Status = 1;
					} else {
						Cooling.C_Fan_Status = 0;
					}
				}
			}
		} else {
			Cooling.C_Fan_Status = 0;
		}
	}
	if ((DoorSwitch_FridgeLeft.Status == 1) && (DoorSwitch_FridgeLeft.OpenTimer_Continuous < TIME_5MIN)) {
		Cooling.C_Fan_Status = 0;
	}
	if ((DoorSwitch_FridgeRight.Status == 1) && (DoorSwitch_FridgeRight.OpenTimer_Continuous < TIME_5MIN)) {
		Cooling.C_Fan_Status = 0;
	}
	if ((DoorSwitch_FreezerLeft.Status == 1) && (DoorSwitch_FreezerLeft.OpenTimer_Continuous < TIME_5MIN)) {
		Cooling.C_Fan_Status = 0;
	}
	if ((DoorSwitch_FreezerRight.Status == 1) && (DoorSwitch_FreezerRight.OpenTimer_Continuous < TIME_5MIN)) {
		Cooling.C_Fan_Status = 0;
	}

	if ((ProductionMode.ExitFlag) && (Cooling.ProductionConFanStopCount <= TIME_30MIN)) {
		Cooling.C_Fan_Status = 0;
	}

	if (DemoFunctionOnFlag == 1) {
		Cooling.R_Fan_Status = 0;
		Cooling.F_FanStatus = 0;
		Cooling.C_Fan_Status = 0;
		Cooling.R_Damper_Status = 0;
		Cooling.C_Damper_Status = 0;
	}
}

//传感器故障控制逻辑
void SensorErrorCooling_Service_250ms(void)
{
	if (WarnningFlag.Temp.R && WarnningFlag.Temp.F) {
		if (Cooling.ErrorRun.RF_RunCount < Error_R_OnTime[Cooling.Amb_Range]) {
			Cooling.R_Damper_Status = 1;
			Cooling.R_Fan_Status = 1;
			Cooling.Valve_Status = Valve_CO_Mode;
			Cooling.CompressorStatus = 1;
			Cooling.CompressorDelayFlag = 0;
			Cooling.F_FanStatus = 0;
			Cooling.C_Fan_Status = 1;
		} else if (Cooling.ErrorRun.RF_RunCount < (Error_F_OnTime[Cooling.Amb_Range] + Error_R_OnTime[Cooling.Amb_Range])) {
			Cooling.Valve_Status = Valve_OC_Mode;
			Cooling.CompressorStatus = 1;
			Cooling.F_FanStatus = 1;
			Cooling.C_Fan_Status = 1;
			Cooling.R_Damper_Status = 0;
			Cooling.R_Fan_Status = 0;
		} else if (Cooling.ErrorRun.RF_RunCount < (Error_R_OffTime[Cooling.Amb_Range] + Error_F_OffTime[Cooling.Amb_Range] + Error_F_OnTime[Cooling.Amb_Range] + Error_R_OnTime[Cooling.Amb_Range])) {
			Cooling.R_Damper_Status = 0;
			Cooling.R_Fan_Status = 0;
			Cooling.CompressorStatus = 0;
			Cooling.CompSpeedProductionFlag = 0;
			Cooling.CompressorDelayOnFlag = 0;
			Cooling.Valve_Status = Valve_CC_Mode;
			Cooling.F_FanStatus = 0;
			Cooling.C_Fan_Status = 0;
		} else {
			Cooling.ErrorRun.RF_RunCount = 0;
		}
	} else {
		Cooling.ErrorRun.RF_RunCount = 0;
	}
}

//强制制冷
void ForceCooling_Service_250ms(void)
{
	if (Cooling.ForceCoolingFlag == 0) return;
	if (FCFunctionCount == 1) {
		Cooling.R_Fan_Status = 1;
		Cooling.F_FanStatus = 1;
		Cooling.C_Fan_Status = 1;
		Cooling.R_Damper_Status = 1;
		Cooling.C_Damper_Status = 1;
		Cooling.CompressorStatus = 1;
		Cooling.Valve_Status = Valve_OO_Mode;
	} else if (FCFunctionCount == 2) {
		Cooling.R_Fan_Status = 0;
		Cooling.F_FanStatus = 1;
		Cooling.C_Fan_Status = 1;
		Cooling.CompressorStatus = 1;
		Cooling.R_Damper_Status = 0;
		Cooling.C_Damper_Status = 0;
		Cooling.Valve_Status = Valve_OC_Mode;
	} else if (FCFunctionCount == 3) {
		Cooling.R_Fan_Status = 1;
		Cooling.F_FanStatus = 0;
		Cooling.C_Fan_Status = 1;
		Cooling.R_Damper_Status = 1;
		Cooling.C_Damper_Status = 1;
		Cooling.CompressorStatus = 1;
		Cooling.Valve_Status = Valve_CO_Mode;
	} else if (FCFunctionCount == 4) {
		Cooling.F_FanStatus = 1;
		Cooling.C_Fan_Status = 1;
		Cooling.CompressorStatus = 1;
		Cooling.Valve_Status = Valve_CO_Mode;
		if (REFRIGE_SENSOR_Q8 >= Temper_Notch_0_CutIn[Cooling.Amb_Range]) {
			Cooling.R_Fan_Status = 1;
			Cooling.R_Damper_Status = 1;
			Cooling.C_Damper_Status = 1;
		} else if (REFRIGE_SENSOR_Q8 <= Temper_Notch_0_CutOff[Cooling.Amb_Range]) {
			Cooling.R_Fan_Status = 0;
			Cooling.R_Damper_Status = 0;
			Cooling.C_Damper_Status = 0;
		}
	} else if (FCFunctionCount == 5) {
		Cooling.R_Fan_Status = 1;
		Cooling.F_FanStatus = 1;
		Cooling.C_Fan_Status = 1;
		Cooling.R_Damper_Status = 1;
		Cooling.C_Damper_Status = 1;
		Cooling.CompressorStatus = 1;
		Cooling.Valve_Status = Valve_CO_Mode;
	}

}
void Cooling_Service_1S(void)
{
	if (Cooling.CompressorDelayFlag == 1) {
		if (Cooling.CompressorDelayCount < 65535) {
			Cooling.CompressorDelayCount++;
		}
		if (Cooling.CompressorDelayCount >= TIME_7MIN) {
			Cooling.CompressorDelayCount = 0;
			Cooling.CompressorDelayFlag = 0;
		}
	} else {
		Cooling.CompressorDelayCount = 0;
	}
	if ((Cooling.CompressorStatus) && (Cooling.CompressorDelayFlag == 0)) {
		Cooling.CompressorRunTimeCount++;
		Cooling.CompressorStopTimeCount = 0;
		if (Cooling.LowAmbCompConRunCount < 65535) {
			Cooling.LowAmbCompConRunCount++;
		}
		if (Cooling.CompRun4hCount < 65535) {
			Cooling.CompRun4hCount++;
		}
	} else {
		Cooling.CompressorRunTimeCount = 0;
		Cooling.CompRun4hCount = 0;
		Cooling.LowAmbCompConRunCount = 0;
		Cooling.CompressorStopTimeCount++;
	}
	if (Cooling.CompRun4hCount >= TIME_4HR) {
		Cooling.CompRun4hCount = 0;
		Cooling.CompConRun4HourFlag = 1;
		if ((REFRIGE_SENSOR_Q8 >= (Cooling.R_CutInNew + TEMP_Q8(5))) && (REFRIGE_SENSOR_Q8 > TEMP_Q8(10))) {
			Defrost.RefHighTemp4hFlag = 1;
		} else {
			Defrost.RefHighTemp4hFlag = 0;
		}
		if (FREEZE_SENSOR_Q8 > TEMP_Q8(-10)) {
			Defrost.FreHighTemp4hFlag = 1;
		} else {
			Defrost.FreHighTemp4hFlag = 0;
		}
	}
	if (Cooling.CompressorRunTimeCount >= TIME_1HR) {
		Defrost.AmbNotSearchFlag = 0;
	}
	if (Cooling.CompressorRunTimeCount >= TIME_6HR) {
		Cooling.CompConRun6HourFlag = 1;
	}
	if (Cooling.CompressorDelayOnFlag) {
		if (Cooling.CompressorDelayOnCount < 255) {
			Cooling.CompressorDelayOnCount++;
		}
	} else {
		Cooling.CompressorDelayOnCount = 0;
	}
	if (Cooling.ForceCoolingFlag == 1) {
		if (++Cooling.ForceCoolingTimeCount >= TIME_72HR) {
			Cooling.ForceCoolingFlag = 0;
			Cooling.ForceCoolingTimeCount = 0;
		}
	} else {
		Cooling.ForceCoolingTimeCount = 0;
	}
	if (Cooling.Cooling_Mode == SUPER_COOL) {
		if (++Cooling.SuperCoolTimerCount >= TIME_8HR) {
			Cooling.SuperCoolTimerCount = 0;
			Cooling.Cooling_Mode = NORMAL;
			Cooling.Notch_R = Cooling.Notch_R_BK;
			Menu_SetRNotch = Cooling.Notch_R;
		}
	} else {
		Cooling.SuperCoolTimerCount = 0;
	}
	if (Cooling.Cooling_Mode == SUPER_FREEZER) {
		if (++Cooling.SuperFreezerTimerCount >= TIME_50HR) {
			Cooling.SuperFreezerTimerCount = 0;
			Cooling.Cooling_Mode = NORMAL;
			Cooling.Notch_F = Cooling.Notch_F_BK;
			Menu_SetFNotch = Cooling.Notch_F;
			Inverter.HighLoadFlag = 0;
			Defrost.SFFirstDefrostFlag = 0;
			Defrost.SFSecondDefrostFlag = 1;
			if (Defrost.SFDefrostExitFlag) {
				Defrost.SFSecondDefrostFlag = 0;
			}
			Defrost.SFDefrostExitFlag = 0;
		}
	} else {
		Cooling.SuperFreezerTimerCount = 0;
		Defrost.SFFirstDefrostFlag = 0;
		Defrost.SFDefrostExitFlag = 0;
	}

	if ((CondesationMode.Status) && (Cooling.CompressorStatus) && (Cooling.CompressorRunTimeCount >= TIME_8MIN)) {
		if (Cooling.CondesationModeFanDelayCount < 65535) {
			Cooling.CondesationModeFanDelayCount++;
		}
	} else {
		Cooling.CondesationModeFanDelayCount = 0;
	}
	if (WarnningFlag.Temp.R && WarnningFlag.Temp.F) {
		if (Cooling.ErrorRun.RF_RunCount < 65535) {
			Cooling.ErrorRun.RF_RunCount++;
		}
	} else {
		Cooling.ErrorRun.RF_RunCount = 0;
	}
	if ((WarnningFlag.Temp.R) && (WarnningFlag.Temp.F == 0)) {
		if (Cooling.ErrorRun.R_RunCount < 65535) {
			Cooling.ErrorRun.R_RunCount++;
		}
	} else {
		Cooling.ErrorRun.R_RunCount = 0;
	}
	if (WarnningFlag.Temp.VV) {
		if (Cooling.ErrorRun.C_RunCount < 65535) {
			Cooling.ErrorRun.C_RunCount++;
		}
	} else {
		Cooling.ErrorRun.C_RunCount = 0;
	}
	if ((WarnningFlag.Temp.F) && (WarnningFlag.Temp.R == 0)) {
		if (Cooling.ErrorRun.F_RunCount < 65535) {
			Cooling.ErrorRun.F_RunCount++;
		}
	} else {
		Cooling.ErrorRun.F_RunCount = 0;
	}
	if (Amb.Update_Time_Sec < 65535) {
		Amb.Update_Time_Sec++;
	}
	if (R_FAN_Amb.R_FAN_Update_Time_Sec < 65535) {
		R_FAN_Amb.R_FAN_Update_Time_Sec++;
	}
	if (Beam_Amb.Beam_Update_Time_Sec < 65535) {
		Beam_Amb.Beam_Update_Time_Sec++;
	}
	if (Cooling.R_Fan_StartDelayFlag) {
		if (Cooling.R_Fan_StartDelayCount < 255) {
			Cooling.R_Fan_StartDelayCount++;
		}
	} else {
		Cooling.R_Fan_StartDelayCount = 0;
	}
	if (Cooling.F_Fan_ValveDelayFlag) {
		if (Cooling.F_Fan_ValveDelayCount < 255) {
			Cooling.F_Fan_ValveDelayCount++;
		}
	} else {
		Cooling.F_Fan_ValveDelayCount = 0;
	}
	if (Cooling.F_Fan_StartDelayFlag) {
		if (Cooling.F_Fan_StartDelayCount < 255) {
			Cooling.F_Fan_StartDelayCount++;
		}
	} else {
		Cooling.F_Fan_StartDelayCount = 0;
	}
	if (Cooling.CompRunCoolingFlag) {
		if (Cooling.CompRunCoolingCount < 65535) {
			Cooling.CompRunCoolingCount++;
		}
	}

	if (Cooling.R_Fan_Status) {
		if (++Cooling.RefFanOnCount >= TIME_60MIN) {
			Cooling.RefFanOnCount = 0;
			Cooling.InoizerOnFlag = 1;
		}
	}
	if (ProductionMode.ExitFlag) {
		if (Cooling.ProductionConFanStopCount <= 65535) {
			Cooling.ProductionConFanStopCount++;
		}
		if (Cooling.ProductionConFanStopCount >= TIME_30MIN) {
			ProductionMode.ExitFlag = 0;
		}
	}
	if (Cooling.Valve_Status == Valve_CO_Mode) {
		if (Cooling.ValveStayInRefCount < 65535) {
			Cooling.ValveStayInRefCount++;
		}
	} else {
		Cooling.ValveStayInRefCount = 0;
	}
	if ((Cooling.Valve_ChangeStatusFlag || Cooling.ValveForceChangeFlag) && (PowerSaveMode.Status)) {
		if (Cooling.ReduceFrostModeCount < 65535) {
			Cooling.ReduceFrostModeCount++;
		}
	} else {
		Cooling.ReduceFrostModeCount = 0;
	}
	if ((Cooling.Amb_Range > Amb_Range_13) && (REFDEF_SENSOR_Q8 < TEMP_Q8(-23)) && (Cooling.CompressorStatus)) {
		if (Cooling.RefDefLowTempCount < 65535) {
			Cooling.RefDefLowTempCount++;
		}
	} else {
		Cooling.RefDefLowTempCount = 0;
	}
	Cooling.PowerOn_Count++;
	if (Cooling.PowerOn_Count >= TIME_30MIN) {
		Cooling.FirstPowerOnFlag = 0;
	}
}